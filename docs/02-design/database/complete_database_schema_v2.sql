-- =====================================================
-- 儿童学习平台数据库设计方案 - V2.1版本
-- 基于优化后的database-design-standards.md规范
-- 用户ID=2, 当前孩子ID=11 的测试环境
-- =====================================================
--
-- 版本更新记录:
-- V2.1 (2025-01-XX): 完善打卡系统业务闭环
--   1. user_camp_participations 表添加 last_checkin_date 字段
--   2. family_contracts 表添加 participation_id 字段
--   3. point_records 表添加 participation_id 字段
--   4. growth_tracks 表添加 participation_id 字段
--   5. 为新增字段添加相应索引，优化查询性能
--   目的: 建立以 participation_id 为核心的数据关联体系，
--        确保打卡系统各模块数据的一致性和可追溯性
-- =====================================================

-- =====================================================
-- 用户管理系统表设计 Planet
-- =====================================================
-- 模块: 用户管理系统 (User Management System)
-- 包含表: users, children, user_children
-- 功能: 用户注册登录、孩子档案管理、多用户孩子管理
-- 创建时间: 2025-07-05
-- =====================================================

-- =====================================================
-- 1. 基础内容系统
-- =====================================================

-- =====================================================
-- 用户表 (users)
-- =====================================================
-- 用途: 存储平台用户的基本信息和账户数据
-- 业务场景: 微信授权登录、用户档案管理、权限控制
-- 设计原则: 遵循微信生态，支持快速注册和登录
-- =====================================================
CREATE TABLE `users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '用户ID，主键自增，全平台唯一标识',
  `openid` varchar(28) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `unionid` char(28) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '微信unionid，28位固定长度，用于跨应用用户识别',
  `nickname` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户昵称，来源于微信昵称或用户自定义，最长50字符',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '用户头像URL，存储微信头像或用户上传头像的完整URL地址',
  `phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '手机号码，可选填写，用于重要通知和账户安全',
  `gender` tinyint unsigned DEFAULT '0' COMMENT '性别 0:未知 1:男 2:女，来源于微信授权信息',
  `province` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '省份，用于地域化推荐和统计分析',
  `city` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '城市，用于本地化服务和用户分组',
  `status` tinyint unsigned DEFAULT '1' COMMENT '账户状态 1:正常 2:禁用 3:注销，用于账户管理和风控',
  `user_type` tinyint unsigned DEFAULT '1' COMMENT '用户类型 1:普通用户 2:VIP用户 3:管理员，用于权限控制',
  `current_child_id` bigint unsigned DEFAULT '0' COMMENT '当前选择的孩子ID，0表示未选择',
  `last_login_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后登录时间，用于用户活跃度分析',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间，NULL表示未删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`) COMMENT '微信openid唯一索引：微信授权登录时快速查找用户',
  KEY `idx_unionid` (`unionid`) COMMENT '微信unionid索引：跨应用用户识别时使用',
  KEY `idx_phone` (`phone`) COMMENT '手机号索引：手机号登录和查找用户时使用',
  KEY `idx_nickname` (`nickname`) COMMENT '昵称索引：用户搜索和模糊查询时使用',
  KEY `idx_created_at` (`created_at`) COMMENT '注册时间索引：按注册时间范围查询用户时使用',
  KEY `idx_last_login` (`last_login_at`) COMMENT '最后登录时间索引：用户活跃度分析时使用'
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表：存储平台用户基本信息，支持微信授权登录，包含统计字段用于排行榜和用户画像分析';


-- =====================================================
-- 孩子档案表 (children)
-- =====================================================
-- 用途: 存储孩子的基本信息和学习档案
-- 业务场景: 孩子档案管理、学习记录跟踪、个性化推荐
-- 设计原则: 保护隐私，支持多维度信息记录
-- =====================================================
CREATE TABLE `children` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '孩子档案ID，主键自增，全平台唯一标识',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '孩子姓名，必填字段，用于档案识别和个性化显示',
  `nickname` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '孩子昵称，可选字段，用于社交展示时的隐私保护',
  `gender` tinyint unsigned DEFAULT '0' COMMENT '性别 0:未知 1:男 2:女，用于个性化推荐和统计分析',
  `birth_date` date DEFAULT NULL COMMENT '出生日期，用于年龄计算和年龄段分组',
  `school` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '学校名称，可选填写，用于同校用户推荐和统计',
  `grade` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '年级班级，如"三年级2班"，用于同龄推荐',
  `province` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '省份，用于地域化推荐和本地活动推送',
  `city` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '城市，用于本地化服务和线下活动组织',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '孩子头像URL，用于社交展示和个人档案',
  `skill_level` tinyint unsigned DEFAULT '1' COMMENT '技能水平 1:新手 2:初级 3:中级 4:高级 5:专业，用于内容推荐',
  `best_score_1min` int unsigned DEFAULT '0' COMMENT '一分钟最佳成绩（个数），用于排行榜和能力评估',
  `best_score_continuous` int unsigned DEFAULT '0' COMMENT '连续跳最佳成绩（个数），用于技能评估',
  `preferred_difficulty` tinyint unsigned DEFAULT '1' COMMENT '偏好难度 1-5级，用于个性化内容推荐',
  `learning_goals` json DEFAULT NULL COMMENT '学习目标JSON数组，如["提高耐力","学会花式跳法"]，用于个性化推荐',
  `privacy_level` tinyint unsigned DEFAULT '2' COMMENT '隐私级别 1:公开 2:好友可见 3:仅自己，控制信息展示范围',
  `show_in_leaderboard` tinyint unsigned DEFAULT '1' COMMENT '是否参与排行榜 0:不参与 1:参与，用户可选择退出排行',
  `total_checkins` int unsigned DEFAULT '0' COMMENT '总打卡次数，冗余字段，提升查询性能',
  `total_points` bigint unsigned DEFAULT '0' COMMENT '总积分，冗余字段，用于快速排行',
  `makeup_used_count` INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已使用补卡次数',
  `makeup_total_count` INT UNSIGNED NOT NULL DEFAULT 3 COMMENT '可用的补卡次数',
  `continuous_days` int unsigned DEFAULT '0' COMMENT '连续打卡天数，用于连续性激励',
  `last_checkin_date` date DEFAULT NULL COMMENT '最后打卡日期，用于连续天数计算',
  `status` tinyint unsigned DEFAULT '1' COMMENT '档案状态 1:正常 2:暂停 3:删除，用于档案管理',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '档案创建时间，记录首次建档时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '档案更新时间，记录最后修改时间',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间，NULL表示未删除',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`) COMMENT '姓名索引：按姓名搜索孩子档案时使用',
  KEY `idx_nickname` (`nickname`) COMMENT '昵称索引：按昵称搜索时使用',
  KEY `idx_birth_date` (`birth_date`) COMMENT '出生日期索引：按年龄段查询和分组时使用',
  KEY `idx_gender` (`gender`) COMMENT '性别索引：按性别统计和推荐时使用',
  KEY `idx_school` (`school`) COMMENT '学校索引：同校推荐和统计时使用',
  KEY `idx_province_city` (`province`,`city`) COMMENT '地域复合索引：地域化推荐时使用',
  KEY `idx_skill_level` (`skill_level`) COMMENT '技能水平索引：按水平分组推荐内容时使用',
  KEY `idx_best_score_1min` (`best_score_1min` DESC) COMMENT '一分钟成绩排行索引：成绩排行榜时使用',
  KEY `idx_best_score_continuous` (`best_score_continuous` DESC) COMMENT '连续跳成绩排行索引：技能排行时使用',
  KEY `idx_total_points` (`total_points` DESC) COMMENT '积分排行索引：积分排行榜时使用',
  KEY `idx_continuous_days` (`continuous_days` DESC) COMMENT '连续打卡排行索引：连续性排行时使用',
  KEY `idx_privacy_status` (`privacy_level`,`status`) COMMENT '隐私状态复合索引：查询可展示档案时使用',
  KEY `idx_leaderboard_status` (`show_in_leaderboard`,`status`) COMMENT '排行榜参与状态索引：生成排行榜时使用',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引：按建档时间查询时使用',
  KEY `idx_last_checkin` (`last_checkin_date`) COMMENT '最后打卡日期索引：连续天数计算时使用'
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='孩子档案表：存储孩子基本信息和学习档案，支持隐私保护和个性化推荐，包含统计字段用于排行榜和性能优化';


-- =====================================================
-- 用户孩子关联表设计
-- =====================================================
-- 表名: user_children
-- 用途: 实现用户与孩子的多对多关系，支持多个用户管理同一个孩子
-- 业务场景: 爸爸妈妈、爷爷奶奶等家庭成员共同管理孩子的学习记录
-- 设计原则: 极简设计，避免复杂的权限控制和家庭概念
-- 创建时间: 2025-07-05
-- =====================================================
CREATE TABLE `user_children` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '关联记录ID，主键自增',
  `user_id` bigint unsigned DEFAULT '0' COMMENT '用户ID，关联users.id，表示孩子的管理者（爸爸/妈妈/爷爷/奶奶等）',
  `child_id` bigint unsigned DEFAULT '0' COMMENT '孩子ID，关联children.id，表示被管理的孩子档案',
  `relation` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '关系描述，如：爸爸、妈妈、爷爷、奶奶、外公、外婆、其他监护人等',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '关联创建时间，记录用户开始管理该孩子的时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间，关系信息变更时自动更新',
  `current_child_id` bigint unsigned DEFAULT '0' COMMENT '当前选择的孩子ID，0表示未选择',
  `deleted_at` timestamp NULL DEFAULT NULL COMMENT '软删除时间，NULL表示未删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_child` (`user_id`,`child_id`) COMMENT '唯一约束：同一用户不能重复关联同一孩子',
  KEY `idx_user_id` (`user_id`) COMMENT '用户ID索引：查询用户管理的所有孩子时使用',
  KEY `idx_child_id` (`child_id`) COMMENT '孩子ID索引：查询孩子的所有管理者时使用',
  KEY `idx_relation` (`relation`) COMMENT '关系类型索引：按关系类型（爸爸/妈妈等）查询时使用',
  KEY `idx_created_at` (`created_at`) COMMENT '创建时间索引：按时间范围查询关联记录时使用',
  CONSTRAINT `fk_user_children_child_id` FOREIGN KEY (`child_id`) REFERENCES `children` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_user_children_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户孩子关联表：实现多用户管理同一孩子的核心表，支持爸爸妈妈等家庭成员共同管理孩子学习';


-- =====================================================
-- 常用查询示例
-- =====================================================

-- 查询用户管理的所有孩子
-- SELECT c.*, uc.relation, uc.created_at as manage_since
-- FROM children c 
-- JOIN user_children uc ON c.id = uc.child_id 
-- WHERE uc.user_id = ? 
-- ORDER BY uc.created_at DESC;

-- 查询孩子的所有管理者
-- SELECT u.id, u.nickname, u.avatar, uc.relation, uc.created_at as manage_since
-- FROM users u 
-- JOIN user_children uc ON u.id = uc.user_id 
-- WHERE uc.child_id = ? 
-- ORDER BY uc.created_at ASC;

-- 检查用户是否可以管理某个孩子（权限验证）
-- SELECT COUNT(*) as can_manage 
-- FROM user_children 
-- WHERE user_id = ? AND child_id = ?;

-- 按关系类型统计
-- SELECT relation, COUNT(*) as count 
-- FROM user_children 
-- GROUP BY relation 
-- ORDER BY count DESC;

-- =====================================================
-- 业务规则说明
-- =====================================================

/*
1. 多对多关系：
   - 一个用户可以管理多个孩子（比如有多个孩子的家庭）
   - 一个孩子可以被多个用户管理（爸爸妈妈爷爷奶奶等）

2. 权限控制：
   - 简化设计：有关联记录就表示有管理权限
   - 所有管理者权限相同，都可以查看和操作孩子的信息
   - 不区分只读、编辑等复杂权限

3. 关系类型：
   - 常见关系：爸爸、妈妈、爷爷、奶奶、外公、外婆
   - 特殊关系：监护人、老师、其他
   - 关系描述用于界面显示和统计分析

4. 数据一致性：
   - 通过外键约束保证数据完整性
   - 用户或孩子删除时，关联记录自动删除
   - 唯一约束防止重复关联

5. 扩展性：
   - 可以根据需要添加更多字段（如权限级别、有效期等）
   - 可以添加状态字段控制关联的启用/禁用
   - 可以添加备注字段记录特殊说明
*/



-- 视频分类表
CREATE TABLE video_categories (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '分类ID',
    name VARCHAR(50) NOT NULL DEFAULT '' COMMENT '分类名称',
    description VARCHAR(200) NOT NULL DEFAULT '' COMMENT '分类描述',
    cover_image VARCHAR(500) NOT NULL DEFAULT '' COMMENT '分类封面图URL',
    parent_id INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '父分类ID，关联video_categories.id',
    sort_order INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序权重',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1:启用 2:禁用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_parent_sort (parent_id, sort_order DESC) COMMENT '父分类排序查询',
    INDEX idx_status (status) COMMENT '状态筛选'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='视频分类表：管理视频内容的分类体系';

-- 视频主表
CREATE TABLE videos (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '视频ID',
    title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '视频标题',
    description TEXT COMMENT '视频描述',
    cover_image VARCHAR(500) NOT NULL DEFAULT '' COMMENT '视频封面图URL',
    video_url VARCHAR(500) NOT NULL DEFAULT '' COMMENT '视频文件URL',
    video_source TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '视频来源 1:微信视频号 2:本地上传',
    duration INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频时长（秒）',
    category_id INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID，关联video_categories.id',
    difficulty_level TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '难度等级 1-5',
    age_group VARCHAR(20) NOT NULL DEFAULT '' COMMENT '适用年龄段',
    tags VARCHAR(500) NOT NULL DEFAULT '' COMMENT '视频标签，逗号分隔',
    view_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '播放次数',
    like_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞数量',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1:正常 2:下架',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间，NULL表示未删除',

    INDEX idx_category_status (category_id, status) COMMENT '分类状态查询',
    INDEX idx_difficulty_age (difficulty_level, age_group) COMMENT '难度年龄筛选',
    INDEX idx_view_count (view_count DESC) COMMENT '热门排序',
    INDEX idx_deleted_at (deleted_at) COMMENT '软删除过滤'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='视频主表：存储教学视频的基本信息和元数据';

-- 视频集合表
CREATE TABLE video_collections (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '视频集合ID',
    title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '集合标题',
    description TEXT COMMENT '集合描述',
    cover_image VARCHAR(500) NOT NULL DEFAULT '' COMMENT '集合封面图URL',
    category_id INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID，关联video_categories.id',
    difficulty_level TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '难度等级 1-5',
    age_group VARCHAR(20) NOT NULL DEFAULT '' COMMENT '适用年龄段',
    is_free TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否免费 0:付费 1:免费',
    free_video_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '免费视频数量',
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格（元）',
    video_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频总数量',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1:正常 2:下架',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_category_status (category_id, status) COMMENT '分类状态查询',
    INDEX idx_price_free (is_free, price) COMMENT '价格筛选'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='视频集合表：管理视频的集合，支持灵活的付费模式';

-- 集合视频关联表
CREATE TABLE collection_videos (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '关联记录ID',
    collection_id BIGINT UNSIGNED NOT NULL COMMENT '视频集合ID，关联video_collections.id',
    video_id BIGINT UNSIGNED NOT NULL COMMENT '视频ID，关联videos.id',
    sort_order INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '视频排序',
    is_free TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否免费 0:付费 1:免费',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_collection_video (collection_id, video_id) COMMENT '防重复关联',
    INDEX idx_collection_sort (collection_id, sort_order) COMMENT '集合排序查询',
    INDEX idx_video_id (video_id) COMMENT '视频查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='集合视频关联表：管理视频集合与具体视频的关联关系';

-- =====================================================
-- 2. 训练营系统
-- =====================================================

-- 训练营主表
CREATE TABLE training_camps (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '训练营ID',
    title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '训练营标题',
    subtitle VARCHAR(300) NOT NULL DEFAULT '' COMMENT '训练营副标题',
    hero_number VARCHAR(50) NOT NULL DEFAULT '' COMMENT '核心数字展示',
    hero_text VARCHAR(100) NOT NULL DEFAULT '' COMMENT '核心文案展示',
    pain_solution VARCHAR(200) NOT NULL DEFAULT '' COMMENT '解决痛点描述',
    duration_days INT UNSIGNED NOT NULL DEFAULT 21 COMMENT '训练营总天数',
    daily_minutes INT UNSIGNED NOT NULL DEFAULT 15 COMMENT '每日建议训练时长（分钟）',
    difficulty_level TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '难度等级 1-5',
    age_group VARCHAR(20) NOT NULL DEFAULT '' COMMENT '适用年龄段',
    price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '价格（元）',
    original_price DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '原价（元）',
    is_free TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否免费 0:付费 1:免费',
    video_collection_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联视频集合ID，关联video_collections.id',
    key_benefits JSON COMMENT '核心收益点',
    promises JSON COMMENT '服务承诺',
    tags JSON COMMENT '训练营标签',
    share_title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '分享标题',
    share_desc VARCHAR(300) NOT NULL DEFAULT '' COMMENT '分享描述',
    wechat_helper VARCHAR(100) NOT NULL DEFAULT '' COMMENT '微信助手号',
    total_participants INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总参与人数',
    completion_rate DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '完成率百分比',
    average_rating DECIMAL(3,2) NOT NULL DEFAULT 0.00 COMMENT '平均评分',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1:正常 2:暂停 3:下线',
    is_featured TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否推荐 0:普通 1:推荐',
    sort_order INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序权重',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间，NULL表示未删除',

    INDEX idx_status_featured (status, is_featured, sort_order DESC) COMMENT '状态推荐排序',
    INDEX idx_difficulty_age (difficulty_level, age_group) COMMENT '难度年龄筛选',
    INDEX idx_video_collection (video_collection_id) COMMENT '视频集合查询',
    INDEX idx_deleted_at (deleted_at) COMMENT '软删除过滤'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='训练营主表：存储训练营的基本信息和配置';

-- 用户训练营参与表
CREATE TABLE user_camp_participations (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '参与记录ID',
    camp_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营ID，关联training_camps.id',
    user_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID，关联users.id',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',
    participation_status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '参与状态 1:进行中 2:已完成 3:已暂停 4:已退出',
    participation_date DATE NOT NULL COMMENT '参与日期',
    current_day INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '当前训练天数',
    progress_percentage DECIMAL(5,2) NOT NULL DEFAULT 0.00 COMMENT '进度百分比',
    total_checkins INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总打卡次数',
    consecutive_days INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '连续打卡天数',
    total_study_minutes INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总学习时长（分钟）',
    last_checkin_date DATE NULL COMMENT '最后打卡日期',
    rating TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户评分 1-5',
    review_text TEXT COMMENT '评价内容',

    -- 新增冗余字段（优化查询性能）
    camp_title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '训练营标题（冗余字段）',
    camp_subtitle VARCHAR(300) NOT NULL DEFAULT '' COMMENT '训练营副标题（冗余字段）',
    total_checkin_days INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总打卡天数（排除跳过的日期）',
    completed_checkin_days INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '已完成打卡天数',


    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_camp_user (camp_id, user_id) COMMENT '训练营用户查询',
    INDEX idx_child_status (child_id, participation_status) COMMENT '孩子状态查询',
    INDEX idx_participation_date (participation_date) COMMENT '参与日期查询',
    UNIQUE KEY uk_camp_child (camp_id, child_id) COMMENT '防重复参与'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='用户训练营参与记录表：记录用户参与训练营的完整生命周期';


-- =====================================================
-- 3. 打卡积分系统
-- =====================================================

-- 训练营打卡日期表（规范化预计算方案）
CREATE TABLE camp_checkin_dates (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '打卡日期记录ID',
    participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '参与记录ID，关联user_camp_participations.id',
    day_number INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '打卡天数序号（1-N）',
    checkin_date DATE NOT NULL COMMENT '具体打卡日期',
    date_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '日期类型 1:正常打卡日 2:跳过日期（周末/节假日）',
    status TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '打卡状态 0:未打卡 1:已打卡 2:补打卡 3:已跳过',
    checkin_record_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联的详细打卡记录ID',

    -- 扩展字段（为未来功能预留）
    is_makeup_allowed TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否允许补卡 0:不允许 1:允许',
    makeup_deadline DATE NULL COMMENT '补卡截止日期',

    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '软删除时间',

    -- 索引设计
    UNIQUE KEY uk_participation_day (participation_id, day_number) COMMENT '防重复',
    INDEX idx_participation_date (participation_id, checkin_date) COMMENT '日期查询',
    INDEX idx_status_date (status, checkin_date) COMMENT '状态统计',
    INDEX idx_checkin_record (checkin_record_id) COMMENT '关联查询',
    INDEX idx_deleted_at (deleted_at) COMMENT '软删除过滤'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='训练营打卡日期表：预计算并存储每个参与者的所有打卡日期和状态';

-- =====================================================
-- 3. 打卡积分系统
-- =====================================================

-- 打卡记录表
CREATE TABLE checkin_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '打卡记录ID',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',
    camp_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营ID，关联training_camps.id',
    user_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '操作用户ID，关联users.id',
    checkin_date DATE NOT NULL COMMENT '打卡日期',
    practice_duration INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '练习时长（分钟）',
    jump_count_1min INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '1分钟跳绳个数',
    jump_count_continuous INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '连续跳绳个数',
    feeling_text TEXT COMMENT '训练感受',
    feeling_score TINYINT UNSIGNED NOT NULL DEFAULT 5 COMMENT '感受评分 1-10',
    photos JSON COMMENT '打卡照片URL数组',
    points_earned INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '获得积分',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1:正常 2:补卡',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间，NULL表示未删除',

    UNIQUE KEY uk_child_date (child_id, checkin_date) COMMENT '每日打卡唯一',
    INDEX idx_camp_id (camp_id) COMMENT '训练营查询',
    INDEX idx_user_id (user_id) COMMENT '用户查询',
    INDEX idx_checkin_date (checkin_date) COMMENT '日期查询',
    INDEX idx_jump_count (jump_count_1min DESC) COMMENT '成绩排序',
    INDEX idx_deleted_at (deleted_at) COMMENT '软删除过滤'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='打卡记录表：记录孩子每日训练打卡的详细信息';

-- 孩子积分统计表
CREATE TABLE child_points (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '积分记录ID',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',
    total_points BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总积分',
    week_points INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '本周积分',
    month_points INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '本月积分',
    total_checkins INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '总打卡次数',
    continuous_days INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '连续打卡天数',
    max_continuous_days INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '最大连续天数',
    week_rank INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '本周排名',
    last_week_rank INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '上周排名',
    last_checkin_date DATE NOT NULL DEFAULT '1970-01-01' COMMENT '最后打卡日期，1970-01-01表示从未打卡',
    last_updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',

    UNIQUE KEY uk_child_id (child_id) COMMENT '孩子唯一',
    INDEX idx_total_points (total_points DESC) COMMENT '总积分排行',
    INDEX idx_week_points (week_points DESC) COMMENT '周积分排行',
    INDEX idx_continuous_days (continuous_days DESC) COMMENT '连续天数排行'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='孩子积分统计表：统计每个孩子的积分和排行榜相关数据';

-- 积分变动记录表
CREATE TABLE point_records (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '积分记录ID',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',
    participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '参与记录ID，关联user_camp_participations.id',
    source_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '来源 1:打卡 2:完成契约 3:系统奖励',
    source_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '来源ID',
    points_change INT NOT NULL DEFAULT 0 COMMENT '积分变化',
    points_after BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '变化后总积分',
    description VARCHAR(200) NOT NULL DEFAULT '' COMMENT '变化描述',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_child_id (child_id) COMMENT '孩子查询',
    INDEX idx_participation_id (participation_id) COMMENT '参与记录查询',
    INDEX idx_source (source_type, source_id) COMMENT '来源查询',
    INDEX idx_created_at (created_at) COMMENT '时间查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='积分变动记录表：记录所有积分变化的详细历史';

-- =====================================================
-- 4. 家庭契约系统
-- =====================================================

-- 家庭荣誉契约表
CREATE TABLE family_contracts (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '契约ID',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',
    camp_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '训练营ID，关联training_camps.id',
    participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '参与记录ID，关联user_camp_participations.id',
    creator_user_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建者用户ID，关联users.id',
    title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '契约标题',
    goal_description VARCHAR(500) NOT NULL DEFAULT '' COMMENT '目标描述',
    reward_description VARCHAR(500) NOT NULL DEFAULT '' COMMENT '奖励描述',
    goal_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '目标类型 1:连续打卡 2:总打卡数 3:成绩达标',
    goal_value INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标数值',
    current_progress INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '当前进度',
    contract_status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1:进行中 2:已完成 3:待授勋 4:已授勋',
    start_date DATE NOT NULL COMMENT '开始日期',
    target_date DATE NOT NULL COMMENT '目标日期',
    completed_date DATE NULL COMMENT '完成日期，NULL表示未完成',
    awarded_date DATE NULL COMMENT '授勋日期，NULL表示未授勋',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间，NULL表示未删除',

    INDEX idx_child_status (child_id, contract_status) COMMENT '孩子状态查询',
    INDEX idx_participation_id (participation_id) COMMENT '参与记录查询',
    INDEX idx_camp_id (camp_id) COMMENT '训练营查询',
    INDEX idx_target_date (target_date) COMMENT '目标日期查询',
    INDEX idx_deleted_at (deleted_at) COMMENT '软删除过滤'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='家庭荣誉契约表：记录家庭契约的完整信息';

-- 契约见证人表
CREATE TABLE contract_witnesses (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '见证记录ID',
    contract_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '契约ID，关联family_contracts.id',
    user_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '见证人用户ID，关联users.id',
    witness_role TINYINT UNSIGNED NOT NULL DEFAULT 2 COMMENT '角色 1:首席见证官 2:见证人',
    invitation_status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1:已邀请 2:已接受 3:已拒绝',
    joined_at TIMESTAMP NULL COMMENT '加入时间，NULL表示未加入',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_contract_id (contract_id) COMMENT '契约查询',
    INDEX idx_user_id (user_id) COMMENT '用户查询',
    UNIQUE KEY uk_contract_user (contract_id, user_id) COMMENT '防重复见证'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='契约见证人表：管理家庭契约的见证人信息';

-- =====================================================
-- 5. 成长系统
-- =====================================================

-- 成长轨迹记录表
CREATE TABLE growth_tracks (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '成长记录ID',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',
    participation_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '参与记录ID，关联user_camp_participations.id',
    milestone_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '里程碑类型 1:首次打卡 2:连续打卡 3:完成训练营 4:获得勋章',
    milestone_title VARCHAR(200) NOT NULL DEFAULT '' COMMENT '里程碑标题',
    milestone_description TEXT COMMENT '里程碑描述',
    milestone_icon VARCHAR(50) NOT NULL DEFAULT '' COMMENT '里程碑图标',
    related_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '关联ID',
    related_type VARCHAR(50) NOT NULL DEFAULT '' COMMENT '关联类型',
    achievement_value VARCHAR(100) NOT NULL DEFAULT '' COMMENT '成就数值',
    achievement_details JSON COMMENT '成就详细数据',
    is_shareable TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否可分享 0:否 1:是',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_child_id (child_id) COMMENT '孩子查询',
    INDEX idx_participation_id (participation_id) COMMENT '参与记录查询',
    INDEX idx_milestone_type (milestone_type) COMMENT '类型查询',
    INDEX idx_created_at (created_at DESC) COMMENT '时间排序'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='成长轨迹记录表：记录孩子的成长里程碑';

-- 勋章定义表
CREATE TABLE medals (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '勋章ID',
    name VARCHAR(100) NOT NULL DEFAULT '' COMMENT '勋章名称',
    description VARCHAR(300) NOT NULL DEFAULT '' COMMENT '勋章描述',
    icon VARCHAR(50) NOT NULL DEFAULT '' COMMENT '勋章图标',
    condition_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '条件类型 1:打卡次数 2:连续天数 3:积分达标',
    condition_value INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '条件数值',
    condition_description VARCHAR(200) NOT NULL DEFAULT '' COMMENT '条件描述',
    medal_level TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '勋章等级 1:铜 2:银 3:金 4:钻石',
    points_reward INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '奖励积分',
    sort_order INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序权重',
    is_active TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '是否启用 0:否 1:是',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    INDEX idx_condition (condition_type, condition_value) COMMENT '条件查询',
    INDEX idx_sort_order (sort_order DESC) COMMENT '排序查询'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='勋章定义表：定义系统中所有可获得的勋章';

-- 孩子勋章记录表
CREATE TABLE child_medals (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '勋章记录ID',
    child_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '孩子ID，关联children.id',
    medal_id INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '勋章ID，关联medals.id',
    is_unlocked TINYINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否已解锁 0:否 1:是',
    current_progress INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '当前进度',
    target_progress INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标进度',
    unlocked_at TIMESTAMP NULL COMMENT '解锁时间，NULL表示未解锁',
    points_earned INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '获得积分',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',

    UNIQUE KEY uk_child_medal (child_id, medal_id) COMMENT '防重复记录',
    INDEX idx_child_unlocked (child_id, is_unlocked) COMMENT '孩子解锁查询',
    INDEX idx_unlocked_at (unlocked_at DESC) COMMENT '解锁时间排序'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='孩子勋章记录表：记录每个孩子的勋章获得情况';

-- =====================================================
-- 6. 社交系统
-- =====================================================

-- 点赞表
CREATE TABLE likes (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '点赞记录ID',
    user_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞用户ID，关联users.id',
    target_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '目标类型 1:打卡记录 2:评论',
    target_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标ID',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',

    INDEX idx_user_id (user_id) COMMENT '用户查询',
    INDEX idx_target (target_type, target_id) COMMENT '目标查询',
    UNIQUE KEY uk_user_target (user_id, target_type, target_id) COMMENT '防重复点赞'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='点赞表：记录用户的点赞行为';

-- 评论表
CREATE TABLE comments (
    id BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT COMMENT '评论ID',
    user_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论用户ID，关联users.id',
    target_type TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '目标类型 1:打卡记录',
    target_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '目标ID',
    parent_id BIGINT UNSIGNED NOT NULL DEFAULT 0 COMMENT '父评论ID',
    content TEXT COMMENT '评论内容',
    like_count INT UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞数',
    status TINYINT UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态 1:正常 2:隐藏',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间，NULL表示未删除',

    INDEX idx_user_id (user_id) COMMENT '用户查询',
    INDEX idx_target (target_type, target_id) COMMENT '目标查询',
    INDEX idx_parent_id (parent_id) COMMENT '父评论查询',
    INDEX idx_deleted_at (deleted_at) COMMENT '软删除过滤'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT='评论表：记录用户的评论信息';

-- =====================================================
-- 数据库设计完成
-- 总计16张表，完整支持儿童学习平台所有功能
-- 严格遵循优化后的database-design-standards.md规范
-- =====================================================
