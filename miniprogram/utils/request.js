// 网络请求封装 - 统一网络请求处理
// 遵循MVS规则：统一代码模板，完善错误处理，基础测试覆盖

const { API, ERROR_CODES } = require("./constants.js");
const { userStorage } = require("./storage.js");
const { handleNetworkError, createError } = require("./error.js");

/**
 * 请求拦截器管理
 */
class InterceptorManager {
  constructor() {
    this.handlers = [];
  }

  /**
   * 添加拦截器
   * @param {function} fulfilled 成功处理函数
   * @param {function} rejected 失败处理函数
   * @returns {number} 拦截器ID
   */
  use(fulfilled, rejected) {
    this.handlers.push({
      fulfilled,
      rejected,
    });
    return this.handlers.length - 1;
  }

  /**
   * 移除拦截器
   * @param {number} id 拦截器ID
   */
  eject(id) {
    if (this.handlers[id]) {
      this.handlers[id] = null;
    }
  }

  /**
   * 执行拦截器
   * @param {any} value 要处理的值
   * @returns {Promise} 处理结果
   */
  async execute(value) {
    let result = value;

    for (const handler of this.handlers) {
      if (handler) {
        try {
          if (handler.fulfilled) {
            result = await handler.fulfilled(result);
          }
        } catch (error) {
          if (handler.rejected) {
            result = await handler.rejected(error);
          } else {
            throw error;
          }
        }
      }
    }

    return result;
  }
}

/**
 * HTTP请求客户端
 */
class HttpClient {
  constructor() {
    this.baseURL = API.BASE_URL;
    this.timeout = API.TIMEOUT;
    this.retryCount = API.RETRY_COUNT;
    this.retryDelay = API.RETRY_DELAY;

    // 拦截器
    this.interceptors = {
      request: new InterceptorManager(),
      response: new InterceptorManager(),
    };

    // 设置默认拦截器
    this._setupDefaultInterceptors();
  }

  /**
   * 设置默认拦截器
   */
  _setupDefaultInterceptors() {
    // 请求拦截器 - 添加认证头
    this.interceptors.request.use(
      (config) => {
        const token = userStorage.getToken();
        const currentChild = userStorage.getCurrentChild();
        if (token) {
          config.header = {
            ...config.header,
            Authorization: `Bearer ${token}`,
            "X-Children-ID": currentChild?.id || 0,
          };
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器 - 统一处理响应
    this.interceptors.response.use(
      (response) => {
        // Debug: 打印响应数据
        console.log("📥 API响应:", {
          url: response.config?.url || "unknown",
          statusCode: response.statusCode,
          data: response.data,
        });

        // 检查业务状态码
        if (response.data && response.data.code !== undefined) {
          if (response.data.code === 0) {
            const result = response.data.data || response.data;
            console.log("✅ API成功返回:", result);
            return result;
          } else {
            console.error("❌ API业务错误:", response.data);
            throw createError(
              ERROR_CODES.SERVER_ERROR,
              response.data.message || "服务器返回错误",
              null,
              { response }
            );
          }
        }
        console.log("✅ API直接返回:", response.data);
        return response.data;
      },
      (error) => {
        console.error("❌ API请求失败:", error);
        return Promise.reject(handleNetworkError(error));
      }
    );
  }

  /**
   * 发送请求
   * @param {object} config 请求配置
   * @returns {Promise} 请求结果
   */
  async request(config) {
    // 合并默认配置
    const finalConfig = {
      url: this.baseURL + config.url,
      method: config.method || "GET",
      data: config.data || {},
      header: {
        "Content-Type": "application/json",
        ...config.header,
      },
      timeout: config.timeout || this.timeout,
      ...config,
    };

    // Debug: 打印请求参数
    console.log("🚀 API请求:", {
      url: finalConfig.url,
      method: finalConfig.method,
      data: finalConfig.data,
      header: finalConfig.header,
    });

    // 执行请求拦截器
    const processedConfig = await this.interceptors.request.execute(
      finalConfig
    );

    // 发送请求（带重试机制）
    return this._requestWithRetry(processedConfig);
  }

  /**
   * 带重试机制的请求
   * @param {object} config 请求配置
   * @param {number} retryCount 剩余重试次数
   * @returns {Promise} 请求结果
   */
  async _requestWithRetry(config, retryCount = this.retryCount) {
    try {
      const response = await this._makeRequest(config);
      return await this.interceptors.response.execute(response);
    } catch (error) {
      // 如果是网络错误且还有重试次数，则重试
      if (retryCount > 0 && this._shouldRetry(error)) {
        console.log(
          `Request failed, retrying... (${retryCount} attempts left)`
        );
        await this._delay(this.retryDelay);
        return this._requestWithRetry(config, retryCount - 1);
      }
      throw error;
    }
  }

  /**
   * 发送实际请求
   * @param {object} config 请求配置
   * @returns {Promise} 请求结果
   */
  _makeRequest(config) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...config,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res);
          } else {
            reject({
              statusCode: res.statusCode,
              data: res.data,
              message: `HTTP ${res.statusCode}`,
            });
          }
        },
        fail: (error) => {
          // 开发环境下提供更友好的错误信息
          let errorMessage = error.errMsg || "Network Error";
          if (
            errorMessage.includes("Failed to fetch") ||
            errorMessage.includes("ERR_CONNECTION_REFUSED")
          ) {
            errorMessage =
              "无法连接到开发服务器，请确保API服务器已启动 (localhost:8080)";
          }

          reject({
            message: errorMessage,
            error,
          });
        },
      });
    });
  }

  /**
   * 判断是否应该重试
   * @param {object} error 错误对象
   * @returns {boolean} 是否应该重试
   */
  _shouldRetry(error) {
    // 网络错误或超时错误可以重试
    return (
      error.message?.includes("timeout") ||
      error.message?.includes("network") ||
      error.statusCode >= 500
    );
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  _delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // HTTP方法快捷方式
  get(url, config = {}) {
    // 处理GET请求的params参数，转换为query string
    if (config.params) {
      const queryString = Object.keys(config.params)
        .filter(
          (key) =>
            config.params[key] !== undefined && config.params[key] !== null
        )
        .map(
          (key) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(
              config.params[key]
            )}`
        )
        .join("&");

      if (queryString) {
        url = url + (url.includes("?") ? "&" : "?") + queryString;
      }

      // 移除params，避免传递到data中
      const { params, ...restConfig } = config;
      config = restConfig;
    }

    return this.request({ ...config, method: "GET", url });
  }

  post(url, data, config = {}) {
    return this.request({ ...config, method: "POST", url, data });
  }

  put(url, data, config = {}) {
    return this.request({ ...config, method: "PUT", url, data });
  }

  delete(url, config = {}) {
    return this.request({ ...config, method: "DELETE", url });
  }

  patch(url, data, config = {}) {
    return this.request({ ...config, method: "PATCH", url, data });
  }
}

// 创建默认实例
const http = new HttpClient();

/**
 * 上传文件
 * @param {string} url 上传地址
 * @param {string} filePath 文件路径
 * @param {object} options 上传选项
 * @returns {Promise} 上传结果
 */
function uploadFile(url, filePath, options = {}) {
  return new Promise((resolve, reject) => {
    const token = userStorage.getToken();

    wx.uploadFile({
      url: http.baseURL + url,
      filePath,
      name: options.name || "file",
      formData: options.formData || {},
      header: {
        Authorization: token ? `Bearer ${token}` : "",
        ...options.header,
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          if (data.code === 0) {
            resolve(data.data);
          } else {
            reject(createError(ERROR_CODES.SERVER_ERROR, data.message));
          }
        } catch (error) {
          reject(createError(ERROR_CODES.SERVER_ERROR, "上传响应解析失败"));
        }
      },
      fail: (error) => {
        reject(handleNetworkError(error, { url, filePath }));
      },
    });
  });
}

/**
 * 下载文件
 * @param {string} url 下载地址
 * @param {object} options 下载选项
 * @returns {Promise} 下载结果
 */
function downloadFile(url, options = {}) {
  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url: url.startsWith("http") ? url : http.baseURL + url,
      header: options.header || {},
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.tempFilePath);
        } else {
          reject(createError(ERROR_CODES.NETWORK_ERROR, "文件下载失败"));
        }
      },
      fail: (error) => {
        reject(handleNetworkError(error, { url }));
      },
    });
  });
}

/**
 * 请求加载状态管理
 */
class LoadingManager {
  constructor() {
    this.loadingCount = 0;
    this.loadingTitle = "加载中...";
  }

  /**
   * 显示加载状态
   * @param {string} title 加载提示文字
   */
  show(title = this.loadingTitle) {
    if (this.loadingCount === 0) {
      wx.showLoading({
        title,
        mask: true,
      });
    }
    this.loadingCount++;
  }

  /**
   * 隐藏加载状态
   */
  hide() {
    this.loadingCount = Math.max(0, this.loadingCount - 1);
    if (this.loadingCount === 0) {
      wx.hideLoading();
    }
  }

  /**
   * 强制隐藏加载状态
   */
  forceHide() {
    this.loadingCount = 0;
    wx.hideLoading();
  }
}

// 创建全局加载管理器
const loading = new LoadingManager();

// 为HTTP客户端添加加载状态管理
http.interceptors.request.use((config) => {
  if (config.showLoading !== false) {
    loading.show(config.loadingTitle);
  }
  return config;
});

http.interceptors.response.use(
  (response) => {
    loading.hide();
    return response;
  },
  (error) => {
    loading.hide();
    return Promise.reject(error);
  }
);

// 导出HTTP客户端实例和工具函数
module.exports = {
  default: http,
  http,
  HttpClient,
  uploadFile,
  downloadFile,
  LoadingManager,
  loading,
};
